<template>
  <div class="architecture-left-panel">
    <!-- 企业/单位选择器 -->
    <div class="mb-8px">
      <a-tree-select
        v-model:value="memberSelectCompany"
        :tree-data="deptAndCompanyTree"
        :tree-line="true && { showLeafIcon: false }"
        :fieldNames="{ label: 'name', value: 'id', children: 'childs' }"
        class="w-full"
        placeholder="选择企业/单位"
        disabled
      />
    </div>

    <!-- 部门左侧区域 -->
    <div class="department-left flex-1 flex flex-col">
      <!-- 搜索过滤区 -->
      <div class="bg-#FAFAFB p-12px box-border mb-12px">
        <a-select
          class="w-full"
          v-model:value="filterValue"
          placeholder="搜索部门"
          :options="headerOption"
          show-search
          :filter-option="filterHeaderOption"
          allowClear
          :field-names="{ label: 'name', value: 'id' }"
          @change="handleSelectTreeFilter"
        >
          <template #option="{ name, full_name }">
            <div class="select-option" :title="name">
              <div class="option-name truncate">{{ name }}</div>
              <div class="option-info text-#999 truncate">
                {{ full_name?.replaceAll('/', '>') }}
              </div>
            </div>
          </template>
        </a-select>
      </div>

      <!-- 树形结构区 -->
      <div class="arch-left-list flex-1 relative overflow-hidden">
        <div class="tree-container" :class="{ 'is-loading': spinning }">
          <!-- 自定义加载指示器 -->
          <div v-if="spinning" class="custom-spin-dot">
            <div class="dot-bottom-left"></div>
            <div class="dot-bottom-right"></div>
          </div>

          <a-spin :spinning="false" class="spin-container" size="large">
            <div class="tree-wrapper">
              <a-tree
                ref="treeRef"
                class="absolute w-full"
                :key="treeKey"
                :fieldNames="treeProps"
                v-model:expandedKeys="expandedKeys"
                v-model:selectedKeys="selectedKeys"
                :tree-data="deptTree"
                :auto-expand-parent="false"
                :selectable="true"
                :virtual="true"
                @select="handleSelectDept"
              >
                <template #title="data">
                  <div class="flex w-full items-center justify-between" :class="{ 'node-selected': String(selectedKeys[0]) === String(data.id_with_type) }">
                    <a-tooltip :title="data.name" v-if="isEllipsis(data.name)" placement="topLeft">
                      <span class="truncate" :data-node-id="String(data.id_with_type)" :style="{ width: `${130 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                    </a-tooltip>
                    <span v-else class="truncate" :data-node-id="String(data.id_with_type)" :style="{ width: `${130 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                    <a-space class="action-icons" :size="0">
                      <a-tooltip placement="bottom" v-if="!isInternalUser">
                        <template #title>
                          <span>添加子部门</span>
                        </template>
                        <PlusOutlined class="text-10px c-#999 mt-8px" @click="handleAddSonDept(data)" />
                      </a-tooltip>
                      <a-dropdown>
                        <MoreOutlined class="c-#999 mt-8px" />
                        <template #overlay>
                          <a-menu>
                            <a-menu-item key="1" @click="handleViewDept(data.id)">
                              <div class="gap-8px flex items-center">
                                <span>查看详情</span>
                              </div>
                            </a-menu-item>
                            <a-menu-item key="2" @click="handleEditDept(data.id)" v-if="!isInternalUser && checkPermission('arch_edit')">
                              <div class="gap-8px flex items-center">
                                <span>编辑信息</span>
                              </div>
                            </a-menu-item>
                            <!-- <a-menu-item key="4" @click="handleMoveUp(data.id, 1)" v-if="checkPermission('arch_updown')">
                              <div class="gap-8px flex items-center">
                                <span>上移</span>
                              </div>
                            </a-menu-item>
                            <a-menu-item key="5" @click="handleMoveDown(data.id, 1)" v-if="checkPermission('arch_updown')">
                              <div class="gap-8px flex items-center">
                                <span>下移</span>
                              </div>
                            </a-menu-item> -->
                            <a-menu-item key="6" @click="handleDeleteDept(data.id)" v-if="!isInternalUser && checkPermission('arch_delete') && btnPermission[211008]">
                              <div class="gap-8px flex items-center text-red-500">
                                <span>删除</span>
                              </div>
                            </a-menu-item>
                          </a-menu>
                        </template>
                      </a-dropdown>
                    </a-space>
                  </div>
                </template>
              </a-tree>
            </div>
          </a-spin>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div class="tree-bottom flex mt-12px">
        <div :class="['expand-all-btn flex cursor-pointer items-center justify-center hover:bg-gray-50 transition-colors', isInternalUser ? 'flex-1' : 'flex-1']" @click="toggleExpandAll">
          <component :is="DoubleRightOutlined" :class="['expand-icon', isAllExpanded ? 'rotate-up' : 'rotate-down']" />
          <span class="expand-text">{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
        </div>
        <div
          class="add-dept-btn flex flex-1 cursor-pointer items-center justify-center text-white hover:bg-blue-600 transition-colors"
          @click="handleAddDept"
          v-if="!isInternalUser && checkPermission('arch_add')"
        >
          <PlusCircleOutlined class="mr-4px" />
          <span class="add-text">新建部门</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { MoreOutlined, DoubleRightOutlined, PlusOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import { GetCompanyTreeAuto, type TreeNode, type SearchOption, DeleteDepartment } from '@/servers/CompanyArchitecture'
import { checkPagePermission } from '@/utils'
import { usePermission } from '@/hook/usePermission'

const { btnPermission } = usePermission()
// 简单的深拷贝函数，避免依赖lodash
const cloneDeep = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map((item) => cloneDeep(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = cloneDeep(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// Props
interface Props {
  // 当前选中的部门/企业ID
  selectedId?: string
  // 权限检查函数
  // eslint-disable-next-line no-unused-vars
  checkPermission?: (_permission: string) => boolean
  // 默认展开的层级数（1表示只展开第一层，2表示展开前两层，以此类推）
  defaultExpandLevels?: number
}

// 修正默认值，确保类型匹配
const props = withDefaults(defineProps<Props>(), {
  selectedId: '',
  // 默认实现：忽略未使用的参数（下划线表示有意未使用）
  checkPermission: () => () => true,
  // 默认展开前两层
  defaultExpandLevels: 1,
})
//  编辑部门
const handleEditDept = (id: string) => {
  emit('edit-dept', id)
}
// 新建部门
const handleAddDept = () => {
  emit('add-dept')
}
// Emits
const emit = defineEmits<{
  // 选择部门/企业变化
  'select-change': [id: string, data: TreeNode | null]
  // 添加部门
  'add-dept': [parentId?: string]
  // 编辑部门
  'edit-dept': [id: string]
  // 查看部门详情
  'view-dept': [id: string, nodeType: number]
  // 部门操作成功（删除、移动等）
  'dept-operation-success': []
}>()

// 响应式数据
const memberSelectCompany = ref<string>('')
const filterValue = ref<any>(null)
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const isAllExpanded = ref(true)
const spinning = ref(false)
const treeRef = ref()
const treeKey = ref(0)
// 标记是否是第一次加载
const isFirstLoad = ref(true)
// 获取当前用户信息，判断是否为内部用户
const getCurrentUserScope = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      return userData.scope || 2 // 默认为外部用户
    }
    return 2 // 默认为外部用户
  } catch (error) {
    console.error('获取用户scope失败:', error)
    return 2 // 默认为外部用户
  }
}

// 判断是否为内部用户（scope=1为内部用户，scope=2为外部用户）
const isInternalUser = computed(() => getCurrentUserScope() === 1)

// 数据源
const deptAndCompanyTree = ref<TreeNode[]>([])
const enterpriseAccountOption = ref<TreeNode[]>([])
const deptTree = ref<TreeNode[]>([])
const headerOption = ref<SearchOption[]>([])

// 树形结构字段映射
const treeProps = ref({
  children: 'childs',
  title: 'name',
  key: 'id_with_type',
})
const handleAddSonDept = (data: TreeNode) => {
  emit('add-dept', String(data.id))
}
// 监听展开状态变化
watch(
  expandedKeys,
  (newVal) => {
    // 获取所有可展开的节点（有子节点的节点）
    const allExpandableKeys = getAllExpandableKeys(deptTree.value)

    // 确保比较时类型一致，都转换为字符串
    const normalizedExpandedKeys = newVal.map((key) => String(key))
    const normalizedExpandableKeys = allExpandableKeys.map((key) => String(key))

    // 简化判断逻辑：只有当所有可展开的节点都在展开列表中时，才认为是全部展开
    if (normalizedExpandableKeys.length === 0) {
      isAllExpanded.value = false
    } else {
      // 注意：切换为 id_with_type 后，expandableKeys 也应基于 id_with_type
      isAllExpanded.value = normalizedExpandableKeys.every((key) => normalizedExpandedKeys.includes(key))
    }
  },
  { deep: true },
)

// 监听外部选中状态变化
watch(
  () => props.selectedId,
  (newVal) => {
    if (newVal && newVal !== selectedKeys.value[0]) {
      selectedKeys.value = [newVal]

      // 查找对应的节点信息以应用样式
      const findNodeForStyle = (nodes: TreeNode[]): TreeNode | null => {
        for (const node of nodes) {
          if (String(node.id_with_type) === String(newVal)) {
            return node
          }
          if (node.childs?.length) {
            const result = findNodeForStyle(node.childs)
            if (result) return result
          }
        }
        return null
      }

      // 延迟应用样式，确保DOM已更新
      nextTick(() => {
        const node = findNodeForStyle(deptTree.value) || findNodeForStyle(enterpriseAccountOption.value)
        if (node) {
          forceApplySelectedStyle(newVal, node.name)
        }
      })
    }
  },
  { immediate: true },
)

// 生命周期
onMounted(async () => {
  // 检查当前用户是否有权限访问用户管理页面
  const hasUserManagementPermission = checkPagePermission('/userLists')

  if (hasUserManagementPermission) {
    await getAllDept('init')
    // changeDeptTree() 会在 getAllDept 中自动调用，不需要在这里重复调用
  } else {
    console.warn('用户无权限访问用户管理页面，跳过组织架构数据加载')
  }
})

// 方法定义
// 获取所有可展开的节点（有子节点的节点）
const getAllExpandableKeys = (data: TreeNode[]): string[] => {
  const keys: string[] = []
  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      // 只有有子节点的节点才是可展开的
      if (node.childs?.length && node.childs.length > 0) {
        keys.push(String(node.id_with_type))
        traverse(node.childs)
      }
    })
  }
  traverse(data)
  return keys
}

const getAllDept = async (type?: string) => {
  try {
    const res = await GetCompanyTreeAuto()
    const data = res.data || []

    // 数据格式转换和标准化
    const normalizeData = (nodes: any[]): TreeNode[] => {
      return nodes.map((node) => {
        // 处理 type 字段：接口返回数字类型，需要转换为字符串格式
        let nodeTypeString: '企业=1' | '单位=2' | '部门=3'
        switch (node.type) {
          case 1:
            nodeTypeString = '企业=1'
            break
          case 2:
            nodeTypeString = '单位=2'
            break
          case 3:
            nodeTypeString = '部门=3'
            break
          default:
            nodeTypeString = '部门=3'
        }

        // 处理 parent_type 字段
        let parentTypeString: '企业=1' | '单位=2' | '部门=3'
        switch (node.parent_type) {
          case 1:
            parentTypeString = '企业=1'
            break
          case 2:
            parentTypeString = '单位=2'
            break
          case 3:
            parentTypeString = '部门=3'
            break
          default:
            parentTypeString = '企业=1'
        }

        const normalizedNode: TreeNode = {
          id_with_type: node.id_with_type || '',
          p_id_with_type: node.p_id_with_type || '',
          id: node.id,
          p_id: node.p_id || 0,
          parent_type: parentTypeString,
          name: node.name || '',
          full_name: node.full_name || '',
          type: nodeTypeString,
          order: node.order || 0,
          oa_id: node.oa_id || 0,
          childs: node.childs && node.childs.length > 0 ? normalizeData(node.childs) : [],

          // 兼容性字段（保留旧字段名以防旧代码使用）
          department_name: node.name || node.department_name || '',
          company_name: node.full_name || node.company_name || '',
        }

        // 保留原始数字类型以便兼容
        ;(normalizedNode as any).originalType = node.type

        return normalizedNode
      })
    }

    const normalizedData = normalizeData(data)

    // 设置层级信息
    const setLevel = (nodes: TreeNode[], level = 1, parentPath = '') => {
      nodes.forEach((node) => {
        node.level = level
        node.class = `level-${level}`
        if (node.childs?.length) {
          setLevel(node.childs, level + 1, `${parentPath}/${node.name}`)
        }
      })
    }

    setLevel(normalizedData)
    enterpriseAccountOption.value = cloneDeep(normalizedData)

    // 过滤企业节点
    const filterNodes = (nodes: TreeNode[]): TreeNode[] => {
      return nodes
        .filter((node) => node.type === '企业=1')
        .map((node) => ({
          ...node,
          childs: node.childs ? filterNodes(node.childs) : [],
        }))
    }

    deptAndCompanyTree.value = filterNodes(normalizedData)

    // 初始化时自动选择第一个企业
    if (type === 'init' && deptAndCompanyTree.value.length > 0) {
      memberSelectCompany.value = String(deptAndCompanyTree.value[0].id)
    }

    // 无论是初始化还是刷新，都需要更新部门树
    if (deptAndCompanyTree.value.length > 0) {
      // console.log('🌳 更新部门树显示...')
      changeDeptTree()
    }

    findHeaderOption()
  } catch (error) {
    console.error('获取组织架构数据失败:', error)
    message.error('获取组织架构数据失败')
  }
}

const changeDeptTree = () => {
  // console.log('🔄 changeDeptTree 被调用，当前企业ID:', memberSelectCompany.value)
  if (!memberSelectCompany.value) {
    // console.log('⚠️ 没有选中的企业ID，清空部门树')
    deptTree.value = []
    return
  }

  const findCompanyTree = (data: TreeNode[], companyId: string): TreeNode[] => {
    for (const item of data) {
      if (String(item.id) === companyId) {
        return [item]
      }
      if (item.childs?.length) {
        const result = findCompanyTree(item.childs, companyId)
        if (result.length > 0) return result
      }
    }
    return []
  }

  const oldTreeLength = deptTree.value.length
  deptTree.value = findCompanyTree(enterpriseAccountOption.value, memberSelectCompany.value)

  console.log('🌲 部门树数据更新:', {
    oldLength: oldTreeLength,
    newLength: deptTree.value.length,
    isFirstLoad: isFirstLoad.value,
    enterpriseDataLength: enterpriseAccountOption.value.length,
  })

  // 只在第一次加载时自动展开前两层
  if (isFirstLoad.value) {
    setTimeout(() => {
      const maxExpandLevel = props.defaultExpandLevels || 2
      const keys: string[] = []

      // console.debug(`🚀 第一次加载，自动展开前${maxExpandLevel}层`)

      const collectExpandKeys = (nodes: TreeNode[], level = 1) => {
        nodes.forEach((node) => {
          // console.debug(`检查节点: ${node.name}, 层级: ${level}, 子节点: ${node.childs?.length || 0}`)

          if (node.childs && node.childs.length > 0 && level <= maxExpandLevel) {
            keys.push(String(node.id_with_type))
            // console.debug(`✅ 添加展开节点: ${node.name} (KEY: ${node.id_with_type})`)

            if (level < maxExpandLevel) collectExpandKeys(node.childs, level + 1)
          }
        })
      }

      collectExpandKeys(deptTree.value)

      // console.debug(`🎯 第一次加载收集到的展开节点:`, keys)

      expandedKeys.value = [...keys]

      // 避免整树强制重渲染，仅轻量 nextTick
      nextTick(() => {})

      isFirstLoad.value = false
    }, 50)
  } else {
    // console.debug('⏭️ 非第一次加载，跳过自动展开')
  }

  // 延迟设置选中状态，确保Tree组件已经渲染完成
  setTimeout(() => {
    if (deptTree.value.length > 0 && deptTree.value[0]) {
      const enterpriseNode = deptTree.value[0]
      const enterpriseNodeKey = String(enterpriseNode.id_with_type)

      // 确保选择的是企业节点（第一级）
      const originalType = (enterpriseNode as any).originalType || enterpriseNode.type
      if (originalType === 1 || enterpriseNode.type === '企业=1') {
        // 直接设置选中状态，并向外抛出事件（不做任何 DOM 操作，交由 a-tree 自身高亮）
        selectedKeys.value = [enterpriseNodeKey]
        emit('select-change', String(enterpriseNode.id), enterpriseNode)
      }
    }
  }, 150)
}

const findHeaderOption = () => {
  const options: SearchOption[] = []

  const traverse = (nodes: TreeNode[], parentPath = '') => {
    nodes.forEach((node) => {
      // 将字符串类型转换为数字类型以匹配 SearchOption 接口
      let typeNumber: number
      const nodeType = node.type as '企业=1' | '单位=2' | '部门=3'
      switch (nodeType) {
        case '企业=1':
          typeNumber = 1
          break
        case '单位=2':
          typeNumber = 2
          break
        case '部门=3':
          typeNumber = 3
          break
        default:
          // 如果是数字类型，直接使用原始值
          typeNumber = (node as any).originalType || 3
      } // 不包括企业节点
      options.push({
        id: String(node.id_with_type),
        name: node.name,
        full_name: parentPath ? `${parentPath}/${node.name}` : node.name,
        type: typeNumber,
      })

      if (node.childs?.length) {
        const currentPath = parentPath ? `${parentPath}/${node.name}` : node.name
        traverse(node.childs, currentPath)
      }
    })
  }

  traverse(enterpriseAccountOption.value)
  headerOption.value = options
}

// 事件处理方法
// 注意：由于企业选择器已被禁用，此方法暂时不会被调用
// const handleCompanyChange = () => {
//   selectedKeys.value = []
//   filterValue.value = null

//   // 切换企业时，不是第一次加载，所以不会自动展开
//   console.log('🔄 切换企业，isFirstLoad:', isFirstLoad.value)

//   changeDeptTree()
//   findHeaderOption()

//   // 注意：changeDeptTree() 中已经会自动选择第一个节点并触发 select-change 事件
//   // 所以这里不需要再次触发事件
// }

// 滚动到选中节点，并在必要时补充选中样式（仅限本组件内，不全局）
const forceApplySelectedStyle = (nodeKey: string, _nodeName: string, opts: { autoScroll?: boolean; parentId?: string } = {}) => {
  const { autoScroll = true } = opts

  const getTreeRoot = (): Element | Document => {
    const inst = treeRef?.value as any
    const candidates = [inst?.$el, inst, document.querySelector('.tree-wrapper'), document.querySelector('.arch-left-list'), document.querySelector('.ant-tree'), document]
    for (const c of candidates) {
      if (c && typeof (c as any).querySelector === 'function') return c as Element | Document
    }
    return document
  }

  nextTick(() => {
    const root = getTreeRoot()
    const q = (sel: string) => ((root as any).querySelector ? (root as Element | Document).querySelector(sel) : null)

    // 先清理本树中已有的选中样式，避免残留（仅作用于本组件根下）
    if ((root as any).querySelectorAll) {
      Array.from((root as Element | Document).querySelectorAll('.ant-tree-treenode-selected')).forEach((n) => n.classList.remove('ant-tree-treenode-selected'))
      Array.from((root as Element | Document).querySelectorAll('.ant-tree-node-content-wrapper-selected')).forEach((n) => n.classList.remove('ant-tree-node-content-wrapper-selected'))
    }

    // 优先用我们埋的 data-node-id（id_with_type）定位
    let el: Element | null = null
    const titleEl = q(`[data-node-id="${nodeKey}"]`)
    if (titleEl) el = (titleEl as HTMLElement).closest('.ant-tree-treenode')

    // 如果还未定位到，则尝试 antd 已选中的 treenode
    if (!el) el = q('.ant-tree-treenode-selected')

    if (el) {
      // 补充选中样式，确保当前选中唯一高亮
      el.classList.add('ant-tree-treenode-selected')
      const cw = el.querySelector('.ant-tree-node-content-wrapper')
      if (cw) cw.classList.add('ant-tree-node-content-wrapper-selected')

      if (autoScroll) {
        const targetEl = el as HTMLElement
        // 寻找最近的可滚动容器（优先组件内的 .arch-left-list）
        const rootEl = (getTreeRoot() as Element) || document.body
        let container: HTMLElement | null = rootEl.querySelector('.arch-left-list') as HTMLElement
        const isScrollable = (node: HTMLElement | null) => {
          if (!node) return false
          const style = window.getComputedStyle(node)
          const overflowY = style.overflowY
          return (overflowY === 'auto' || overflowY === 'scroll') && node.scrollHeight > node.clientHeight
        }
        if (!container || !isScrollable(container)) {
          // 向上寻找最近的滚动容器
          let p: HTMLElement | null = targetEl.parentElement
          while (p && p !== document.body && !isScrollable(p)) p = p.parentElement
          container = isScrollable(p) ? p : null
        }
        if (container) {
          const containerRect = container.getBoundingClientRect()
          const elRect = targetEl.getBoundingClientRect()
          const currentScrollTop = container.scrollTop
          const offsetTop = elRect.top - containerRect.top + currentScrollTop
          const targetScrollTop = offsetTop - container.clientHeight / 2 + targetEl.offsetHeight / 2
          container.scrollTo({ top: Math.max(0, targetScrollTop), behavior: 'smooth' })
        } else {
          // 兜底：使用视口居中
          const elRect = targetEl.getBoundingClientRect()
          window.scrollTo({
            top: window.scrollY + elRect.top - window.innerHeight / 2 + targetEl.offsetHeight / 2,
            behavior: 'smooth',
          })
        }
      }
    }
  })
}

const handleSelectDept = (selectedKeysValue: string[]) => {
  if (selectedKeysValue.length > 0) {
    const selectedKey = selectedKeysValue[0] // id_with_type

    const findNode = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (String(node.id_with_type) === String(selectedKey)) {
          console.log('✅ 找到匹配节点:', node.name, 'KEY:', node.id_with_type)
          return node
        }
        if (node.childs?.length) {
          const result = findNode(node.childs)
          if (result) return result
        }
      }
      return null
    }

    const selectedNode = findNode(deptTree.value) || findNode(enterpriseAccountOption.value)

    if (selectedNode) {
      // 高亮 + 滚动
      forceApplySelectedStyle(selectedKey, selectedNode.name, { autoScroll: true, parentId: String(selectedNode.p_id_with_type || selectedNode.p_id) })
    }

    // 对外事件仍传数值 id
    emit('select-change', String(selectedNode?.id || ''), selectedNode)
  } else {
    console.log('⚠️ selectedKeysValue 为空，不处理选择')
  }
}

const handleSelectTreeFilter = async (newVal: string) => {
  if (!newVal) {
    selectedKeys.value = []
    return
  }

  const targetKey = String(newVal) // id_with_type
  console.log('🔎 搜索选择触发: targetKey=', targetKey)

  // 在完整树中查找路径（keys 使用 id_with_type；ids 用于企业切换）
  const findPathKeys = (nodes: TreeNode[], key: string, pathKeys: string[] = [], pathIds: string[] = []): { keys: string[]; ids: string[] } | null => {
    for (const node of nodes) {
      const currKeys = [...pathKeys, String(node.id_with_type)]
      const currIds = [...pathIds, String(node.id)]
      if (String(node.id_with_type) === key) return { keys: currKeys, ids: currIds }
      if (node.childs?.length) {
        const res = findPathKeys(node.childs, key, currKeys, currIds)
        if (res) return res
      }
    }
    return null
  }

  const found = findPathKeys(enterpriseAccountOption.value, targetKey)
  console.log('🧭 计算到的全路径(keys/ids):', found)

  if (found && found.keys.length) {
    const fullKeys = found.keys
    const fullIds = found.ids

    // 第一个一定是企业，依据 id（不是 id_with_type）判断是否需要切换企业
    const enterpriseId = fullIds[0]
    const needSwitchCompany = memberSelectCompany.value !== enterpriseId
    console.log('🏢 企业判断:', { current: memberSelectCompany.value, targetEnterprise: enterpriseId, needSwitchCompany })
    if (needSwitchCompany) {
      memberSelectCompany.value = enterpriseId
      changeDeptTree()
      await nextTick()
    }

    // 展开父级路径（不包含自身）并选中目标节点（使用 id_with_type）
    const parentPathKeys = fullKeys.slice(0, -1)
    expandedKeys.value = Array.from(new Set([...expandedKeys.value, ...parentPathKeys]))
    selectedKeys.value = [targetKey]
    console.log('📂 更新展开与选中:', { parentPathKeys, expandedKeys: expandedKeys.value, selectedKeys: selectedKeys.value })

    // 查找节点对象（根据 id_with_type 匹配）
    const findNodeByKey = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (String(node.id_with_type) === targetKey) return node
        if (node.childs?.length) {
          const r = findNodeByKey(node.childs)
          if (r) return r
        }
      }
      return null
    }

    const targetNode = findNodeByKey(deptTree.value) || findNodeByKey(enterpriseAccountOption.value)
    console.log('🎯 选中节点对象:', targetNode)

    // 等待一次渲染后再滚动；改为轮询就绪（更快比固定1s）
    await nextTick()
    if (targetNode) {
      let tries = 0
      const tryScroll = () => {
        tries++
        forceApplySelectedStyle(targetKey, targetNode.name, { autoScroll: true, parentId: String(targetNode.p_id_with_type || targetNode.p_id) })
        if (tries < 5) setTimeout(tryScroll, 120)
      }
      tryScroll()
    }

    // 对外事件：传出数值 id，保持兼容
    emit('select-change', String(targetNode?.id || ''), targetNode)
    return
  }

  // 兜底：当前企业树中查找（使用 id_with_type）
  const findAndExpandPath = (nodes: TreeNode[], key: string, path: string[] = []): boolean => {
    for (const node of nodes) {
      const currPath = [...path, String(node.id_with_type)]
      if (String(node.id_with_type) === key) {
        expandedKeys.value = Array.from(new Set([...expandedKeys.value, ...path]))
        selectedKeys.value = [key]
        console.log('📂 兜底展开:', { path, expandedKeys: expandedKeys.value })
        emit('select-change', String(node.id), node)
        return true
      }
      if (node.childs?.length) {
        if (findAndExpandPath(node.childs, key, currPath)) return true
      }
    }
    return false
  }

  findAndExpandPath(deptTree.value, targetKey)
}

const filterHeaderOption = (input: string, option: any) => {
  return option.name.toLowerCase().includes(input.toLowerCase())
}

const toggleExpandAll = () => {
  // console.debug('🔄 toggleExpandAll 点击:', {
  //   currentState: isAllExpanded.value,
  //   action: isAllExpanded.value ? '收起全部' : '展开全部',
  //   currentExpandedKeys: expandedKeys.value.length,
  //   currentExpandedKeysArray: expandedKeys.value,
  //   treeData: deptTree.value.length,
  // })

  spinning.value = true

  if (isAllExpanded.value) {
    // 收起全部 - 使用最简单直接的方法
    console.log('🔽 执行收起全部，清空展开状态')

    // 直接设置为空数组
    expandedKeys.value = []

    console.log('🔽 收起全部执行完成，当前状态:', expandedKeys.value)
  } else {
    // 展开全部 - 展开所有有子节点的节点
    const expandableKeys = getAllExpandableKeys(deptTree.value).map((key) => String(key))
    expandedKeys.value = expandableKeys
  }

  // 不再强制重新渲染Tree组件，直接关闭加载态
  // treeKey.value++
  // console.debug('🔄 强制更新树组件，新的 treeKey:', treeKey.value)

  setTimeout(() => {
    spinning.value = false
    console.log('🔄 操作完成，最终状态:', {
      expandedKeys: expandedKeys.value,
      expandedKeysLength: expandedKeys.value.length,
      isAllExpanded: isAllExpanded.value,
    })
  }, 300)
}

// 操作方法
const handleViewDept = (id: string) => {
  console.log('查看部门详情:', id)

  // 查找当前节点信息以获取类型
  const findNode = (nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (String(node.id) === String(id)) {
        return node
      }
      if (node.childs?.length) {
        const result = findNode(node.childs)
        if (result) return result
      }
    }
    return null
  }

  const currentNode = findNode(deptTree.value) || findNode(enterpriseAccountOption.value)

  // 根据节点类型确定type参数：企业传1，部门传3
  let nodeType = 3 // 默认为部门
  if (currentNode) {
    const originalType = (currentNode as any).originalType || currentNode.type
    if (originalType === 1 || currentNode.type === '企业=1') {
      nodeType = 1 // 企业
    } else if (originalType === 3 || currentNode.type === '部门=3') {
      nodeType = 3 // 部门
    }
  }

  emit('view-dept', id, nodeType)
}
const handleDeleteDept = async (id: string) => {
  try {
    // 二次确认弹窗（使用 Modal.confirm）
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除部门？删除前，请先移除部门内所有人员再进行操作，否则会操作失败.',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger', // 确认按钮为危险色
      // 点击确认按钮的回调
      onOk: async () => {
        spinning.value = true // 显示加载状态
        try {
          // 调用删除接口
          await DeleteDepartment({
            id,
            p_id: '',
            company_id: memberSelectCompany.value,
            name: '',
            header_ids: 0,
            oa_id: '',
          })
          message.success('删除成功')
          await getAllDept() // 刷新部门树
          emit('dept-operation-success') // 通知父组件
        } catch (error: any) {
          const errorMessage = error?.message || '删除失败'
          message.error(errorMessage)
        } finally {
          spinning.value = false // 关闭加载状态
        }
      },
      // 点击取消按钮的回调（可选）
      onCancel: () => {
        // 无需操作，弹窗会自动关闭
      },
    })
  } catch (error) {
    console.error('弹窗操作异常:', error)
  }
}
// 检测文本是否需要省略号
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是设置的最大宽度
  return width > 130
}

// 暴露方法给父组件
defineExpose({
  refresh: getAllDept,
  getSelectedCompany: () => memberSelectCompany.value,
  getSelectedDept: () => selectedKeys.value[0] || '',
})
</script>

<style lang="scss" scoped>
.architecture-left-panel {
  position: relative;
  z-index: 1;
  display: flex !important;
  flex-direction: column !important;
  flex-grow: 0 !important; /* 防止扩展 */
  flex-shrink: 0 !important; /* 防止被压缩 */
  width: 230px !important;
  min-width: 230px !important;
  max-width: 230px !important;
  height: 100%;
  margin-right: 16px;
}

.department-left {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
}

.arch-left-list {
  flex: 1;
  min-height: 300px;
  max-height: calc(100vh - 200px);
  overflow: visible auto !important;
  border-top: 1px solid #f0f0f0;
  overflow-x: hidden;
  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      min-width: 22px;
      height: 100%;
      padding: 0;
      margin: 0;

      .ant-tree-switcher-icon,
      .anticon {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &.ant-tree-switcher_close {
        // 对于前两层节点，强制显示为展开状态
        .anticon-caret-right {
          transform: rotate(90deg) !important;
        }
      }
    }

    &.force-selected {
      color: #409eff !important;
      background-color: #eef2fa !important;

      .ant-tree-node-content-wrapper {
        color: #409eff !important;
        background-color: #eef2fa !important;
      }
    }
  }
&::-webkit-scrollbar-thumb {
    background-color: #c1c1c1; // 滑块颜色（目标颜色）
  }
  // 选中状态样式 - 使用更高的优先级确保样式生效
  :deep(.ant-tree-treenode-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa !important;
  }

  // 选中节点的内容包装器样式
  :deep(.ant-tree-node-content-wrapper-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }

  // 强制为选中的节点添加样式（通过动态类名）
  :deep(.force-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }

    &.ant-tree-treenode {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  // 确保选中状态在hover时也保持正确的样式
  // 全屏遮罩
  .fullscreen-mask {
    position: fixed;
    inset: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(255 255 255 / 70%);
  }

  :deep(.ant-tree-treenode-selected:hover) {
    color: #409eff !important;
    background-color: #eef2fa !important;

    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }

  // 树形容器样式
  .tree-container {
    position: relative;
    height: 100%;
    min-height: 300px;

    &.is-loading {
      &::before {
        position: absolute;
        inset: 0;
        z-index: 999;
        content: '';
        background: rgb(255 255 255 / 80%);
      }
    }

    .spin-container {
      height: 100%;

      .tree-wrapper {
        position: relative;
        height: 100%;
      }
    }
  }

  // 自定义Ant Design风格的加载指示器
  .custom-spin-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1000;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    animation: antSpinRotate 1.2s infinite linear;

    &::before,
    &::after,
    .dot-bottom-left,
    .dot-bottom-right {
      position: absolute;
      width: 6px;
      height: 6px;
      content: '';
      background: #1890ff;
      border-radius: 50%;
      animation: antSpinDotSpin 1s infinite linear alternate;
    }

    &::before {
      top: 0;
      left: 0;
      animation-delay: 0s;
    }

    &::after {
      top: 0;
      right: 0;
      animation-delay: 0.4s;
    }

    .dot-bottom-left {
      bottom: 0;
      left: 0;
      animation-delay: 0.8s;
    }

    .dot-bottom-right {
      right: 0;
      bottom: 0;
      animation-delay: 1.2s;
    }
  }

  // Ant Design风格的动画
  @keyframes antSpinRotate {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes antSpinDotSpin {
    0% {
      opacity: 0.3;
      transform: scale(0.75);
    }

    50% {
      opacity: 1;
      transform: scale(1);
    }

    100% {
      opacity: 0.3;
      transform: scale(0.75);
    }
  }

  // 完全隐藏原始的ant-spin指示器
  :deep(.tree-container) {
    .ant-spin {
      .ant-spin-dot {
        display: none !important;
      }

      .ant-spin-text {
        display: none !important;
      }
    }

    // 确保ant-spin容器不影响布局
    .ant-spin-container {
      position: static !important;
    }
  }
}

// 局部确保选中高亮（不影响全局）：
:deep(.node-selected) {
  color: #409eff !important;
  background-color: #eef2fa !important;

  .ant-tree-node-content-wrapper {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }
}

// 移除复杂的节点样式，使用简洁的参考样式
.tree-bottom {
  overflow: hidden;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;

  .expand-all-btn {
    padding: 8px;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    transition: all 0.2s;

    .expand-icon {
      margin-right: 4px;
      font-size: 14px;
      color: #666;
      transition: transform 0.2s;
    }

    .expand-text {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
    }

    &:hover {
      background-color: #f5f5f5;

      .expand-icon {
        color: #1890ff;
      }

      .expand-text {
        color: #1890ff;
      }
    }
  }

  .add-dept-btn {
    padding: 8px;
    background: #1890ff;
    transition: all 0.2s;

    .add-text {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
    }

    &:hover {
      background-color: #40a9ff;
    }
  }
}

.rotate-down {
  transform: rotate(90deg);
}

.rotate-up {
  transform: rotate(-90deg);
}

.select-option {
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  .option-name {
    margin-bottom: 2px;
    font-weight: 500;
    color: #262626;
  }

  .option-info {
    font-size: 12px;
    color: #8c8c8c;
  }
}

// 企业选择器样式优化
:deep(.ant-tree-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}

// 搜索框样式优化
:deep(.ant-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}
</style>
